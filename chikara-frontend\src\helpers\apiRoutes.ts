import { orpc } from "@/lib/orpc";

export const APIROUTES = {
    // Authentication Routes (better-auth)
    AUTH: {
        LOGIN: "/auth/login",
        REQPASSWORDRESET: "/auth/requestPasswordReset",
        PASSWORDRESET: "/auth/resetPassword",
        LOGOUT: "/auth/logout",
        // Registration Routes (ORPC)
        REGISTER: orpc.auth.register.key(),
        CHECKUSERNAME: orpc.auth.checkUsername.key(),
        COMPLETEREGISTRATION: "/registration/completeRegistration", // TODO: Convert to OR<PERSON>
    },

    // Admin Routes
    ADMIN: {
        CHATBAN: "/admin/chatban",
        HIDEMESSAGE: "/admin/hide-single-message",
        UNHIDEMESSAGE: "/admin/unhide-single-message",
        DELETEMESSAGE: "/admin/delete-single-message",
        GANGINFO: "/admin/gang-info?id=",
    },

    // User Routes
    USER: {
        FACULTYLIST: orpc.user.getUserList.key(),
        CURRENTUSERINFO: "/user/current-user-info",
        USERINFO: orpc.user.getUserInfo.key(),
        INVENTORY: orpc.user.getInventory.key(),
        EQUIPPEDITEMS: orpc.user.getEquippedItems.key(),
        EQUIPPEDCONSUMABLES: "/user/equipped-consumables",
        EQUIPCONSUMABLE: "/user/equip-consumable",
        UNEQUIPCONSUMABLE: "/user/unequip-consumable",
        TRADEABLEINVENTORY: orpc.user.getTradeableInventory.key(),
        STATUSEFFECTS: orpc.user.getStatusEffects.key(),
        UPDATEPROFILEDETAILS: orpc.user.updateProfileDetails.key(),
        TRAIN: orpc.user.train.key(),
        EQUIP: orpc.user.equipItem.key(),
        UNEQUIP: orpc.user.unequipItem.key(),
        USEITEM: orpc.user.useItem.key(),
        GAMECONFIG: orpc.user.getGameConfig.key(),
        LINKDISCORD: orpc.user.linkDiscord.key(),
        SETLASTNEWSIDREAD: orpc.user.setLastNewsIDRead.key(),
        SKILLS: orpc.user.getSkills.key(),
    },

    // Profile Routes
    PROFILE: {
        VIEWCOMMENTS: orpc.profileComment.getComments.key(),
        CREATECOMMENT: orpc.profileComment.postComment.key(),
    },

    // Registration Codes Routes (ORPC)
    REGISTRATION_CODES: {
        REFERRALCODELIST: orpc.auth.getReferralCodes.key(),
        CHECKCODE: orpc.auth.checkCode.key(),
    },

    // Gang Routes
    GANG: {
        GANGLIST: "/gang/list",
        GANGINFO: "/gang/gang-info?id=",
        CREATEGANG: "/gang/create",
        CURRENTGANGINFO: "/gang/current-gang",
        MEMBERSHARES: "/gang/member-shares?id=",
        GANGLOGS: "/gang/gang-logs?id=",
        GANGINVITELIST: "/gang/invite-list",
        HASGANGSIGIL: "/gang/has-gang-sigil",
        GANGINVITE: "/gang/invite",
        CURRENTINVITES: "/gang/current-invites",
        ACCEPTINVITE: "/gang/accept-invite",
        DECLINEINVITE: "/gang/decline-invite",
        ASSIGNRANK: "/gang/assign-rank",
        UPDATESHARES: "/gang/update-payout-shares",
        UPGRADEHIDEOUT: "/gang/upgrade-hideout",
        LEAVEGANG: "/gang/leave",
        REQUESTINVITE: "/gang/request-invite",
        UPDATEGANGINFO: "/gang/update-gang-info",
        KICKMEMBER: "/gang/kick-member",
    },

    // Items Routes
    ITEMS: {
        GETUPGRADEITEMS: orpc.item.getUpgradeItems.key(),
        UPGRADEITEM: orpc.item.upgradeItem.key(),
        GETITEMLIST: orpc.admin.item.list.key(), // Dev only route
    },

    // Bank Routes
    BANK: {
        WITHDRAW: orpc.bank.withdraw.key(),
        DEPOSIT: orpc.bank.deposit.key(),
        TRANSFER: orpc.bank.transfer.key(),
        BANKTRANSACTIONS: orpc.bank.getBankTransactions.key(),
    },

    // Shops Routes
    SHOPS: {
        SHOPLIST: orpc.shop.shopList.key(),
        SHOPINFO: orpc.shop.shopInfo.key(),
        CREATESHOP: orpc.shop.admin.createShop.key(),
        EDITSHOP: orpc.shop.admin.updateShop.key(),
        DELETESHOP: orpc.shop.admin.deleteShop.key(),
        CREATESHOPLISTING: orpc.shop.admin.createShopListing.key(),
        EDITSHOPLISTING: orpc.shop.admin.editShopListing.key(),
        DELETESHOPLISTING: orpc.shop.admin.deleteShopListing.key(),
        PURCHASEITEM: orpc.shop.purchaseItem.key(),
        SELLITEM: orpc.shop.sellItem.key(),
        GETTRADERREP: orpc.shop.getTraderRep.key(),
    },

    // Job Routes
    JOBS: {
        JOBLIST: orpc.job.list.key(),
        CURRENTJOBINFO: orpc.job.info.key(),
        APPLYFORJOB: orpc.job.apply.key(),
        APPLYFORPROMOTION: orpc.job.promote.key(),
        GETJOBLVLREQS: orpc.job.getRequirements.key(),
        CHANGEPAYOUTTIME: orpc.job.changePayoutTime.key(),
    },

    BATTLE: {
        BATTLEBEGIN: "/battle/begin",
        ATTACK: "/battle/attack",
        FLEE: "/battle/flee",
        POSTBATTLEACTION: "/battle/postBattleAction",
        STATUS: "/battle/status",
    },

    // Chat Routes
    CHAT: {
        HISTORY: orpc.chat.getHistory.key(),
        ROOMS: orpc.chat.getRooms.key(),
    },

    // Infirmary Routes
    INFIRMARY: {
        INFIRMARYLIST: orpc.infirmary.getHospitalList.key(),
        INJUREDLIST: orpc.infirmary.getInjuredList.key(),
        REVIVEPLAYER: orpc.infirmary.revivePlayer.key(),
        CHECKIN: orpc.infirmary.hospitalCheckIn.key(),
    },

    // Jail Routes
    JAIL: {
        JAILLIST: orpc.jail.jailList.key(),
        JAILBAIL: orpc.jail.bail.key(),
    },

    // Property Routes
    PROPERTY: {
        HOUSINGLIST: orpc.property.getHousingList.key(),
        PURCHASE: orpc.property.purchaseProperty.key(),
        SELL: orpc.property.sellProperty.key(),
        SETPRIMARY: orpc.property.setPrimaryProperty.key(),
    },

    // Crafting Routes
    CRAFTING: {
        GETQUEUE: orpc.crafting.getCraftingQueue.key(),
        RECIPELIST: orpc.crafting.getRecipes.key(),
        CREATERECIPE: orpc.crafting.createRecipe.key(),
        EDITRECIPE: orpc.crafting.editRecipe.key(),
        DELETERECIPE: orpc.crafting.deleteRecipe.key(),
        CRAFTITEM: orpc.crafting.craftItem.key(),
        COMPLETECRAFT: orpc.crafting.completeCraft.key(),
        CANCELCRAFT: orpc.crafting.cancelCraft.key(),
        ADMINLIST: orpc.crafting.getAdminRecipeList.key(),
    },

    // Private Messaging Routes
    MESSAGING: {
        HISTORY: orpc.privateMessage.getChatHistory.key(),
        UNREAD: orpc.privateMessage.getUnreadCount.key(),
        SENDMESSAGE: orpc.privateMessage.sendMessage.key(),
        READMESSAGE: orpc.privateMessage.markMessageRead.key(),
    },

    // Roguelike Routes
    ROGUELIKE: {
        CURRENTMAP: orpc.roguelike.getCurrentMap.key(),
        BEGIN: orpc.roguelike.beginRun.key(),
        ADVANCE: orpc.roguelike.advance.key(),
        ACTIVATENODE: orpc.roguelike.activateNode.key(),
        SCAVENGEOPTION: orpc.roguelike.chooseScavengeOption.key(),
    },

    // Notifications Routes
    NOTIFICATIONS: {
        HISTORY: orpc.notification.getList.key(),
        UNREAD: orpc.notification.getUnreadCount.key(),
        READ: orpc.notification.markRead.key(),
        UPDATEPUSHNOTIFICATIONSETTINGS: orpc.notification.updatePushSettings.key(),
        SAVEFCMTOKEN: orpc.notification.saveFCMToken.key(),
    },

    // leaderboards Routes
    LEADERBOARDS: {
        GETBOARDS: orpc.leaderboard.getLeaderBoards.key(),
        // GETEMOTEBOARDS: orpc.leaderboard.getChatEmoteLeaderboards.key(),
    },

    // Courses/Dojo Routes
    COURSES: {
        COURSELIST: orpc.course.list.key(),
        STARTCOURSE: orpc.course.start.key(),
    },

    // Casino Routes
    CASINO: {
        SLOTS: orpc.casino.gamble.key(),
        LOTTERY: orpc.casino.getLottery.key(),
        ENTERLOTTERY: orpc.casino.enterLottery.key(),
        CHECKENTRY: orpc.casino.checkLotteryEntry.key(),
        ROULETTEBET: orpc.casino.placeBet.key(),
    },

    // Special/Unique Items Routes
    SPECIALITEMS: {
        DEATHNOTE: orpc.item.useDeathNote.key(),
        LIFENOTE: orpc.item.useLifeNote.key(),
        KOMPROMAT: orpc.item.useKompromat.key(),
        MEGAPHONE: orpc.item.useMegaphone.key(),
        DAILYCHEST: orpc.item.useDailyChest.key(),
        DAILYCHESTITEMS: orpc.item.getDailyChestItems.key(),
        REDEEM_MATERIALS_CRATE: orpc.item.useMaterialsCrate.key(),
        REDEEM_TOOLS_CRATE: orpc.item.useToolsCrate.key(),
    },

    // Pets Routes
    PETS: {
        LIST: "/pets/list",
        FEED: "/pets/feed",
        PLAY: "/pets/play",
        TRAIN: "/pets/train",
        CUSTOMIZE: "/pets/customize",
        EVOLVE: "/pets/evolve",
        SETACTIVE: "/pets/set-active",
    },

    // Talents Routes
    TALENTS: {
        GETTALENTS: "/talents/list",
        GETUNLOCKEDTALENTS: "/talents/unlocked",
        UNLOCKTALENT: "/talents/unlock",
        EQUIPABILITY: "/talents/equipAbility",
        UNEQUIPABILITY: "/talents/unequipAbility",
        RESETTALENTS: "/talents/reset",
    },

    // Quests/Tasks Routes
    QUESTS: {
        GETQUESTLIST: "/quests/list",
        GETQUESTPROGRESS: "/quests/progress",
        AVAILABLEQUESTS: "/quests/available",
        STARTQUEST: "/quests/start",
        COMPLETEQUEST: "/quests/complete",
        GETCOMBINEDQUESTLIST: "/quests/combined-quests-list",
        ACTIVEQUESTS: "/quests/active",
        COMPLETEDQUESTS: "/quests/completed",
        HANDINITEM: "/quests/hand-in-item",
    },

    // Bounty Routes
    BOUNTIES: {
        BOUNTYLIST: orpc.bounty.getBountyList.key(),
        ACTIVEBOUNTYLIST: orpc.bounty.getActiveBountyList.key(),
        PLACEBOUNTY: orpc.bounty.placeBounty.key(),
        DELETEBOUNTY: orpc.bounty.deleteBounty.key(),
    },

    // Suggestions Routes
    SUGGESTIONS: {
        SUGGESTIONLIST: "/suggestions/list",
        VOTEHISTORY: "/suggestions/voteHistory",
        COMMENTS: "/suggestions/comments?id=",
        CREATESUGGESTION: "/suggestions/create",
        SUGGESTIONVOTE: "/suggestions/vote",
        SUGGESTIONCOMMENT: "/suggestions/comment",
        UPDATESUGGESTIONSTATE: "/suggestions/change-state",
        AVAILABLEPOLLS: "/suggestions/available-polls",
        CREATEPOLLRESPONSE: "/suggestions/poll-response",
        POLLRESULTS: "/suggestions/poll-results?pollId=",
    },

    MISSIONS: {
        MISSIONLIST: orpc.mission.getList.key(),
        STARTMISSION: orpc.mission.start.key(),
        CANCELMISSION: orpc.mission.cancel.key(),
        CURRENTMISSION: orpc.mission.getCurrent.key(),
    },

    SHRINE: {
        DAILYGOAL: orpc.shrine.getGoal.key(),
        GETDONATIONS: orpc.shrine.getDonations.key(),
        DONATE: orpc.shrine.donate.key(),
        ACTIVESHRINEBUFF: orpc.shrine.getActiveBuff.key(),
        ISBUFFACTIVE: orpc.shrine.isBuffActive.key(),
    },

    AUCTIONS: {
        AUCTIONLIST: orpc.auction.getList.key(),
        CREATEAUCTIONLISTING: orpc.auction.createListing.key(),
        BUYOUTLISTING: orpc.auction.buyoutListing.key(),
        CANCELLISTING: orpc.auction.cancelListing.key(),
    },

    ROOFTOP: {
        NPCLIST: "/battle/rooftopList",
        BEGIN: "/battle/begin-npc",
    },

    SOCIAL: {
        FRIENDLIST: "/social/friends",
        FRIENDREQUESTS: "/social/friends/requests",
        SENDFRIENDREQUEST: "/social/friends/request",
        RESPONDFRIENDREQUEST: "/social/friends/request/respond",
        REMOVEFRIEND: "/social/friends/",
        UPDATEFRIENDNOTE: "/social/friends/note",
        UPDATESTATUSMESSAGE: "/social/status-message",
        TOGGLEPRIVACYSETTINGS: "/social/privacy",
        GETRIVALLIST: "/social/rivals",
        ADDRIVAL: "/social/rivals/add",
        REMOVERIVAL: "/social/rivals/",
        UPDATERIVALNOTE: "/social/rivals/note",
    },

    SCAVENGING: {
        GENERATE_GRID: orpc.skills.scavenging.generateGrid.key(),
        REVEAL_CELL: orpc.skills.scavenging.revealCell.key(),
        ACTIVE_SESSION: orpc.skills.scavenging.getActiveSession.key(),
        DEV_GRID: orpc.skills.scavenging.devGrid.key(),
        END_SESSION: orpc.skills.scavenging.endSession.key(),
        RESET_GRID: orpc.skills.scavenging.resetGrid.key(),
    },

    MINING: {
        START: orpc.skills.startMining.key(),
        PROCESS_SWING: orpc.skills.processSwing.key(),
        SESSION: orpc.skills.getMiningSession.key(),
        CANCEL: orpc.skills.cancelMining.key(),
    },

    // Explore Routes
    EXPLORE: {
        MAP: "/explore/map",
        INTERACT: "/explore/interact",
    },
};
